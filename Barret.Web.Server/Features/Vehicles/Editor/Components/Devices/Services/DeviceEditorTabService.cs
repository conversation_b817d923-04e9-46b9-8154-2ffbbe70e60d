using Barret.Core.Areas.Devices.Enums;
using Barret.Shared.DTOs.Devices;
using Barret.Web.Server.Shared.Components.DeviceEditors.Tabs;
using Barret.Web.Server.Shared.Components.DeviceManagers;
using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Services
{
    /// <summary>
    /// Service for managing device editor tabs.
    /// </summary>
    public class DeviceEditorTabService
    {
        private readonly ILogger<DeviceEditorTabService> _logger;
        private readonly IDeviceEditorConfigurationService _configurationService;

        // Component type mapping for settings tabs
        private readonly Dictionary<string, Type> _settingsComponentTypes = new()
        {
            ["CameraTab"] = typeof(CameraTab),
            ["EngineTab"] = typeof(EngineTab),
            ["ThrusterTab"] = typeof(ThrusterTab),
            ["RadarTab"] = typeof(RadarTab),
            ["LightTab"] = typeof(LightTab),
            ["RudderTab"] = typeof(RudderTab),
            ["HornTab"] = typeof(HornTab),
            ["AntennaTab"] = typeof(AntennaTab),
            ["AutopilotTab"] = typeof(AutopilotTab)
        };

        /// <summary>
        /// Initializes a new instance of the <see cref="DeviceEditorTabService"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="configurationService">The configuration service.</param>
        public DeviceEditorTabService(
            ILogger<DeviceEditorTabService> logger,
            IDeviceEditorConfigurationService configurationService)
        {
            _logger = logger;
            _configurationService = configurationService;
        }

        /// <summary>
        /// Gets the tabs for a device.
        /// </summary>
        /// <param name="device">The device.</param>
        /// <returns>The tabs for the device.</returns>
        public List<DeviceTabInfo> GetTabsForDevice(DeviceDto device)
        {
            if (device == null)
            {
                _logger.LogWarning("Attempted to get tabs for null device");
                return [];
            }

            _logger.LogInformation("Getting tabs for device role: {DeviceRole}", device.DeviceRole);

            var roleConfig = _configurationService.GetConfigurationForRole(device.DeviceRole);
            var tabs = new List<DeviceTabInfo>();

            // Process tabs in order
            var orderedTabs = roleConfig.Tabs
                .Where(kvp => kvp.Value.Enabled)
                .OrderBy(kvp => kvp.Value.Order)
                .ToList();

            _logger.LogInformation("Found {TabCount} enabled tabs for device role {DeviceRole}", orderedTabs.Count, device.DeviceRole);

            foreach (var (tabKey, tabConfig) in orderedTabs)
            {
                var tabInfo = CreateTabInfo(tabKey, tabConfig);
                if (tabInfo != null)
                {
                    tabs.Add(tabInfo);
                    _logger.LogInformation("Added tab: {TabName} for device role {DeviceRole}", tabInfo.Name, device.DeviceRole);
                }
                else
                {
                    _logger.LogWarning("Failed to create tab info for tab key: {TabKey}", tabKey);
                }
            }

            _logger.LogInformation("Returning {TabCount} tabs for device role {DeviceRole}", tabs.Count, device.DeviceRole);
            return tabs;
        }

        /// <summary>
        /// Creates a tab info object based on the tab key and configuration
        /// </summary>
        /// <param name="tabKey">The tab key</param>
        /// <param name="tabConfig">The tab configuration</param>
        /// <returns>The tab info or null if the tab type is not recognized</returns>
        private DeviceTabInfo? CreateTabInfo(string tabKey, TabConfiguration tabConfig)
        {
            return tabKey.ToLowerInvariant() switch
            {
                "general" => new DeviceTabInfo { Name = "General", ComponentType = typeof(BasicInfoTab) },
                "makemodel" => new DeviceTabInfo { Name = "Make/Model", ComponentType = typeof(ModelTab) },
                "position" => CreatePositionTabInfo(tabConfig),
                "connection" => new DeviceTabInfo { Name = "Connection", ComponentType = typeof(ConnectionTab) },
                "settings" => CreateSettingsTabInfo(tabConfig),
                "alarms" => new DeviceTabInfo { Name = "Alarms", ComponentType = typeof(AlarmManagerTab) },
                _ => null
            };
        }

        /// <summary>
        /// Creates a position tab info based on the configuration
        /// </summary>
        /// <param name="tabConfig">The tab configuration</param>
        /// <returns>The position tab info</returns>
        private DeviceTabInfo CreatePositionTabInfo(TabConfiguration tabConfig)
        {
            var isMaritimePosition = tabConfig.Type?.ToLowerInvariant() == "maritime";
            return new DeviceTabInfo
            {
                Name = "Position",
                ComponentType = isMaritimePosition ? typeof(MaritimePositionTab) : typeof(PositionTab)
            };
        }

        /// <summary>
        /// Creates a settings tab info based on the configuration
        /// </summary>
        /// <param name="tabConfig">The tab configuration</param>
        /// <returns>The settings tab info or null if no valid component type is specified</returns>
        private DeviceTabInfo? CreateSettingsTabInfo(TabConfiguration tabConfig)
        {
            if (string.IsNullOrEmpty(tabConfig.ComponentType))
            {
                return null;
            }

            if (_settingsComponentTypes.TryGetValue(tabConfig.ComponentType, out var componentType))
            {
                return new DeviceTabInfo { Name = "Settings", ComponentType = componentType };
            }

            _logger.LogWarning("Unknown settings component type: {ComponentType}", tabConfig.ComponentType);
            return null;
        }

        /// <summary>
        /// Gets the configuration for a device role (backward compatibility method)
        /// </summary>
        /// <param name="role">The device role</param>
        /// <returns>The configuration for the device role</returns>
        public DeviceTabConfiguration GetConfigurationForRole(DeviceRole role)
        {
            var roleConfig = _configurationService.GetConfigurationForRole(role);

            // Convert to legacy format for backward compatibility
            var supportsAlarms = roleConfig.Tabs.TryGetValue("alarms", out var alarmsTab) && alarmsTab.Enabled;

            Type? settingsTabType = null;
            if (roleConfig.Tabs.TryGetValue("settings", out var settingsTab) &&
                settingsTab.Enabled &&
                !string.IsNullOrEmpty(settingsTab.ComponentType) &&
                _settingsComponentTypes.TryGetValue(settingsTab.ComponentType, out settingsTabType))
            {
                // settingsTabType is already set
            }

            return new DeviceTabConfiguration
            {
                SettingsTabType = settingsTabType,
                SupportsAlarms = supportsAlarms
            };
        }
    }

    /// <summary>
    /// Information about a device tab.
    /// </summary>
    public class DeviceTabInfo
    {
        /// <summary>
        /// Gets or sets the name of the tab.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the type of the component to render.
        /// </summary>
        public Type ComponentType { get; set; } = null!;
    }

    /// <summary>
    /// Configuration for a device tab.
    /// </summary>
    public class DeviceTabConfiguration
    {
        /// <summary>
        /// Gets or sets the type of the settings tab.
        /// </summary>
        public Type? SettingsTabType { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the device supports alarms.
        /// </summary>
        public bool SupportsAlarms { get; set; }
    }
}
