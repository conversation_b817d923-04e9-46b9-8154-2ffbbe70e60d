using Barret.Core.Areas.Devices.Enums;
using Barret.Services.Core.Areas.DeviceModels.Queries;
using Barret.Services.Core.Areas.Manufacturers;
using Barret.Shared.DTOs.Devices;
using Barret.Web.Server.Features.Shared;
using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.ViewModels;
using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Services;
using Barret.Web.Server.Services;
using DevExpress.Blazor;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen;
using System;
using System.Collections.Generic;
using System.Reactive.Linq;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Views
{
    /// <summary>
    /// Base class for the device editor view.
    /// </summary>
    public class DeviceEditorViewBase : ViewBase<DeviceEditorViewModel>
    {
        /// <summary>
        /// Gets or sets the callback for when a device is saved.
        /// </summary>
        [Parameter]
        public EventCallback<DeviceDto> OnSaveDevice { get; set; }

        /// <summary>
        /// Gets or sets the callback for when device properties change.
        /// </summary>
        [Parameter]
        public EventCallback<DeviceDto> OnDevicePropertyChanged { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the editor is for adding an interface.
        /// </summary>
        [Parameter]
        public bool IsAddingInterface { get; set; }

        /// <summary>
        /// Gets or sets the device to edit/add.
        /// </summary>
        [Parameter]
        public DeviceDto? Device { get; set; }

        /// <summary>
        /// Gets or sets the manufacturers list.
        /// </summary>
        [Parameter]
        public List<ManufacturerInfo>? Manufacturers { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this is adding mode.
        /// </summary>
        [Parameter]
        public bool IsAdding { get; set; }

        /// <summary>
        /// Gets or sets the device model query service.
        /// </summary>
        [Inject]
        protected IDeviceModelQueryService DeviceModelQueryService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the manufacturer service.
        /// </summary>
        [Inject]
        protected IManufacturerService ManufacturerService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the toast notification service.
        /// </summary>
        [Inject]
        protected IBarretToastNotificationService ToastService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the tab service.
        /// </summary>
        [Inject]
        protected DeviceEditorTabService TabService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the view model logger.
        /// </summary>
        [Inject]
        protected ILogger<DeviceEditorViewModel> ViewModelLogger { get; set; } = null!;

        /// <summary>
        /// Gets or sets the dialog service.
        /// </summary>
        [Inject]
        protected Radzen.DialogService DialogService { get; set; } = null!;

        /// <summary>
        /// Event callback for when the visibility changes.
        /// </summary>
        protected void VisibleChanged(bool value)
        {
            ViewModel.IsVisible = value;
            StateHasChanged();
        }

        /// <summary>
        /// Initializes the component.
        /// </summary>
        protected override void OnInitialized()
        {
            // Create the view model
            ViewModel = new DeviceEditorViewModel(
                DeviceModelQueryService,
                ManufacturerService,
                ToastService,
                ViewModelLogger);

            // Wire up the device property changed event
            ViewModel.DevicePropertyChanged += OnViewModelDevicePropertyChanged;
        }

        /// <summary>
        /// Method called when parameters are set.
        /// </summary>
        protected override async Task OnParametersSetAsync()
        {
            Logger.LogInformation("OnParametersSetAsync called - Device: {DeviceNotNull}, Manufacturers: {ManufacturersNotNull}, IsAdding: {IsAdding}",
                Device != null, Manufacturers != null, IsAdding);

            // Initialize the dialog when parameters are provided
            if (Device != null && Manufacturers != null)
            {
                Logger.LogInformation("Initializing ViewModel with Device Role: {DeviceRole}", Device.DeviceRole);

                if (IsAdding)
                {
                    await ViewModel.OpenForAddAsync(Device, Manufacturers);
                }
                else
                {
                    await ViewModel.OpenForEditAsync(Device, Manufacturers);
                }

                Logger.LogInformation("ViewModel initialized - Device in ViewModel: {ViewModelDeviceNotNull}", ViewModel.Device != null);
                StateHasChanged();
            }
            else
            {
                Logger.LogWarning("Cannot initialize ViewModel - Device: {DeviceNotNull}, Manufacturers: {ManufacturersNotNull}",
                    Device != null, Manufacturers != null);
            }
        }

        /// <summary>
        /// Opens the editor for adding a new device.
        /// </summary>
        /// <param name="device">The device to add.</param>
        /// <param name="manufacturers">The manufacturers.</param>
        public async Task OpenForAddAsync(DeviceDto device, List<ManufacturerInfo> manufacturers)
        {
            await ViewModel.OpenForAddAsync(device, manufacturers);
            StateHasChanged();
        }

        /// <summary>
        /// Opens the editor for editing an existing device.
        /// </summary>
        /// <param name="device">The device to edit.</param>
        /// <param name="manufacturers">The manufacturers.</param>
        public async Task OpenForEditAsync(DeviceDto device, List<ManufacturerInfo> manufacturers)
        {
            await ViewModel.OpenForEditAsync(device, manufacturers);
            StateHasChanged();
        }

        /// <summary>
        /// Reloads the device models.
        /// </summary>
        public async Task ReloadDeviceModelsAsync()
        {
            await ViewModel.UpdateFilteredModelsAsync();
            StateHasChanged();
        }

        /// <summary>
        /// Gets the icon class for a tab.
        /// </summary>
        /// <param name="tabName">The tab name.</param>
        /// <returns>The icon class.</returns>
        protected string GetIconClassForTab(string tabName)
        {
            return tabName switch
            {
                "General" => "bi bi-info-circle",
                "Make/Model" => "bi bi-tag",
                "Position" => "bi bi-geo-alt",
                "Connection" => "bi bi-ethernet",
                "Settings" => "bi bi-gear",
                "Alarms" => "bi bi-exclamation-triangle",
                "Connections" => "bi bi-diagram-3",
                _ => "bi bi-question-circle"
            };
        }

        /// <summary>
        /// Handles manufacturer selection change.
        /// </summary>
        /// <param name="manufacturerId">The manufacturer ID.</param>
        protected async Task OnManufacturerChangedAsync(Guid manufacturerId)
        {
            ViewModel.SelectedManufacturerId = manufacturerId;
            await ViewModel.UpdateFilteredModelsAsync();
        }

        /// <summary>
        /// Handles device change.
        /// </summary>
        /// <param name="device">The updated device.</param>
        protected void OnDeviceChanged(DeviceDto device)
        {
            // Update the device in the view model
            ViewModel.Device.Alarms = device.Alarms;
            ViewModel.Device.Connections = device.Connections;

            // Notify that device properties have changed
            ViewModel.NotifyDevicePropertyChanged();
        }

        /// <summary>
        /// Handles device property changes from tab components.
        /// </summary>
        protected async Task OnTabPropertyChanged()
        {
            // Notify the ViewModel that a device property has changed
            ViewModel.NotifyDevicePropertyChanged();
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Handles device property changes from the ViewModel.
        /// </summary>
        /// <param name="device">The device with changed properties.</param>
        private async void OnViewModelDevicePropertyChanged(DeviceDto device)
        {
            try
            {
                // Invoke the callback to notify the parent component
                if (OnDevicePropertyChanged.HasDelegate)
                {
                    await OnDevicePropertyChanged.InvokeAsync(device);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error handling device property change from ViewModel");
            }
        }



        /// <summary>
        /// Handles save button click.
        /// </summary>
        protected async Task SaveDeviceAsync()
        {
            try
            {
                // Execute the save command
                var observable = ViewModel.SaveCommand.Execute();

                // Subscribe to the observable to get the result
                DeviceDto? savedDevice = null;
                await observable.ForEachAsync(device => savedDevice = device);

                // Invoke the callback if we have a device and a delegate
                if (savedDevice != null && OnSaveDevice.HasDelegate)
                {
                    await OnSaveDevice.InvokeAsync(savedDevice);
                }

                // Close the dialog after successful save
                DialogService.Close(savedDevice);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error saving device");
                ToastService.ShowToast("Error", $"Error saving device: {ex.Message}", ToastType.Error);
            }
        }

        /// <summary>
        /// Handles cancel button click.
        /// </summary>
        protected void CancelEdit()
        {
            DialogService.Close(null);
        }

        /// <summary>
        /// Disposes resources.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Unsubscribe from events
                if (ViewModel != null)
                {
                    ViewModel.DevicePropertyChanged -= OnViewModelDevicePropertyChanged;
                }
            }

            // Call base dispose
            base.Dispose(disposing);
        }
    }
}
