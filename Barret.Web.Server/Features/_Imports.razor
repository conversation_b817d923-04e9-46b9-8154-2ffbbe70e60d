@using System
@using System.Net.Http
@using System.Collections.Generic
@using System.Linq
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using Microsoft.Extensions.Logging

@* Barret Core *@
@using Barret.Core.Areas.Devices.Enums
@using Barret.Core.Areas.Devices.Models
@using Barret.Core.Areas.Vehicles.Models
@using Barret.Core.Areas.Vehicles.Models.Vessel

@* Barret Shared DTOs *@
@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Vehicles
@using Barret.Shared.DTOs.Vehicles.Vessels

@* Barret Web Server *@
@using Barret.Web.Server
@using Barret.Web.Server.Shared
@using Barret.Web.Server.Services

@* Features *@
@using Barret.Web.Server.Features
@using Barret.Web.Server.Features.Shared
@using Barret.Web.Server.Extensions

@* Home Feature *@
@using Barret.Web.Server.Features.Home.Data
@using Barret.Web.Server.Features.Home.ViewModels
@using Barret.Web.Server.Features.Home.Views

@* Vehicles Feature *@
@using Barret.Web.Server.Features.Vehicles.Data
@using Barret.Web.Server.Features.Vehicles.List.ViewModels
@using Barret.Web.Server.Features.Vehicles.List.Views

@* Radzen Components *@
@using Radzen.Blazor

@* Barret Components *@
@using Barret.Web.Server.Shared.Components
@using Barret.Web.Server.Features.Shared.Components.Layout
@using Barret.Web.Server.Features.Shared.Components.BarretDevExpress
@* Removed references to deleted components:
@using Barret.Web.Server.Shared.Components.Cards
*@
