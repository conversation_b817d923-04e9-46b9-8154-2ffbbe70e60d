@using Barret.Core.Areas.Devices.Enums
@using Barret.Core.Areas.Devices.Extensions
@using Barret.Shared.DTOs.Devices
@using Radzen.Blazor
@using Microsoft.Extensions.Logging
@inject ILogger<RoleSelector> Logger
@inject Radzen.DialogService DialogService

<div class="barret-role-selector-dialog">
    <div class="barret-dialog-content">
        <div class="barret-dialog-header">
            <div class="flex items-center">
                <i class="bi bi-list-ul text-blue-600 mr-2 text-xl"></i>
                <span class="text-lg font-semibold text-gray-900">Select Device Role</span>
            </div>
        </div>

        <div class="barret-dialog-body">
            <div class="mb-4">
                <p class="text-gray-600 mb-4">Select a compatible device role for the interface:</p>

                @if (IsLoading)
                {
                    <div class="flex justify-center items-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <span class="ml-3 text-gray-600">Loading...</span>
                    </div>
                }
                else if (ErrorMessage != null)
                {
                    <div class="barret-alert barret-alert-error">
                        <div class="flex items-center">
                            <i class="bi bi-exclamation-triangle text-red-600 mr-2"></i>
                            <span class="text-red-800">@ErrorMessage</span>
                        </div>
                    </div>
                }
                else if (CompatibleRoles == null || !CompatibleRoles.Any())
                {
                    <div class="barret-alert barret-alert-warning">
                        <div class="flex items-center">
                            <i class="bi bi-exclamation-triangle text-yellow-600 mr-2"></i>
                            <span class="text-yellow-800">No compatible device roles found for this device model.</span>
                        </div>
                    </div>
                }
                else
                {
                    <div class="space-y-2">
                        @foreach (var role in CompatibleRoles)
                        {
                            <button type="button"
                                    class="w-full flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-blue-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    @onclick="() => SelectRole(role)">
                                <span class="text-gray-900 font-medium">@role</span>
                                <i class="bi bi-chevron-right text-gray-400"></i>
                            </button>
                        }
                    </div>
                }
            </div>
        </div>

        <div class="barret-dialog-footer">
            <div class="barret-dialog-btn-group-right">
                <RadzenButton Text="Cancel"
                             Icon="cancel"
                             ButtonStyle="ButtonStyle.Secondary"
                             Click="@HandleCancel"
                             class="barret-btn barret-form-btn" />
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public DeviceDto SourceDevice { get; set; }

    [Parameter]
    public List<DeviceRole> CompatibleRoles { get; set; } = new();

    [Parameter]
    public EventCallback<DeviceRole> OnRoleSelected { get; set; }

    [Parameter]
    public EventCallback OnCancelled { get; set; }

    // Flag to track if operation is in progress to avoid duplicate callbacks
    private bool _operationInProgress;

    // Public properties for component state
    public bool IsLoading { get; private set; }
    public string ErrorMessage { get; private set; }

    /// <summary>
    /// Initializes the component when parameters are set
    /// </summary>
    protected override void OnParametersSet()
    {
        // Load compatible roles when the component is initialized
        LoadCompatibleRoles();
    }

    /// <summary>
    /// Loads compatible roles for the current source device
    /// </summary>
    private void LoadCompatibleRoles()
    {
        IsLoading = true;
        ErrorMessage = null;

        try
        {
            // If we have compatible roles passed as a parameter, use those
            // Otherwise, fall back to the DeviceRoleExtensions if we have a source device
            if (CompatibleRoles != null && CompatibleRoles.Count > 0)
            {
                Logger.LogDebug("Using {Count} compatible roles from parameter", CompatibleRoles.Count);
            }
            else if (SourceDevice != null)
            {
                Logger.LogDebug("Loading compatible roles for device {DeviceId} with role {DeviceRole}",
                    SourceDevice.Id, SourceDevice.DeviceRole);

                // Use the DeviceRoleExtensions to get compatible roles
                CompatibleRoles = SourceDevice.DeviceRole.GetCompatibleRoles().ToList();
                Logger.LogDebug("Found {Count} compatible roles using DeviceRoleExtensions", CompatibleRoles.Count);
            }
            else
            {
                CompatibleRoles = new List<DeviceRole>();
                Logger.LogDebug("No source device or compatible roles provided");
            }

            if (CompatibleRoles.Count == 0)
            {
                ErrorMessage = "No compatible roles available.";
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Error loading compatible roles: {ex.Message}";
            Logger.LogError(ex, "Error loading compatible roles for device {DeviceId}", SourceDevice?.Id);
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }



    /// <summary>
    /// Handles role selection and closes the dialog with the selected role
    /// </summary>
    private async Task SelectRole(DeviceRole role)
    {
        // Prevent multiple operations
        if (_operationInProgress) return;

        try
        {
            Logger.LogDebug("Role selected: {Role}", role);
            _operationInProgress = true;

            // Close the dialog with the selected role as the result
            DialogService.Close(role);
        }
        finally
        {
            _operationInProgress = false;
        }
    }

    /// <summary>
    /// Handles cancel button click and closes the dialog without a result
    /// </summary>
    private async Task HandleCancel()
    {
        // Prevent multiple operations
        if (_operationInProgress) return;

        try
        {
            Logger.LogDebug("Role selection cancelled by user");
            _operationInProgress = true;

            // Close the dialog without a result (null)
            DialogService.Close(null);
        }
        finally
        {
            _operationInProgress = false;
        }
    }
}